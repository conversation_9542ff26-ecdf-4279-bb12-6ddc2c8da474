package test

import (
	"context"
	"testing"

	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/controller/islamic"
	"halalplus/app/islamic-content-svc/internal/logic/prayer"
	"halalplus/utility/page"

	"github.com/gogf/gf/v2/test/gtest"
)

func TestGetHajiNewsList(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 初始化控制器
		controller := &islamic.ControllerPrayer{}
		
		// 创建请求
		req := &v1.HajiNewsListReq{
			Page: &page.PageRequest{
				Page: 1,
				Size: 10,
			},
		}

		// 调用接口
		res, err := controller.GetHajiNewsList(context.Background(), req)
		
		// 验证结果
		t.AssertNil(err)
		t.AssertNE(res, nil)
		t.AssertEQ(res.Code, 200)
		t.AssertEQ(res.Msg, "success")
		t.Assert<PERSON>(res.Data, nil)
		
		// 验证数据结构
		t.AssertNE(res.Data.List, nil)
		t.AssertNE(res.Data.Page, nil)
		
		// 打印结果用于调试
		t.Logf("Response: %+v", res)
	})
}

func TestPrayerServiceGetHajiNewsList(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 prayer service 实例
		prayerService := prayer.New()
		
		// 调用 service 方法
		output, err := prayerService.GetHajiNewsList(context.Background(), 2, 1, 10) // 使用印尼语
		
		// 验证结果
		t.AssertNil(err)
		t.AssertNE(output, nil)
		t.AssertNE(output.List, nil)
		t.AssertGE(output.Total, 0)
		
		// 打印结果用于调试
		t.Logf("Service Output: %+v", output)
		if len(output.List) > 0 {
			t.Logf("First item: %+v", output.List[0])
		}
	})
}
