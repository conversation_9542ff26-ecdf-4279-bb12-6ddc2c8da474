syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";
import "google/protobuf/wrappers.proto";

// 日历查询请求
message CalendarReq {
  int32 year = 1;                           // 公历年份
  int32 month = 2;                          // 公历月份
  string method_code = 3;                   // 计算方法：AUTO, LFNU, UMMUL_QURA
  int32 date_adjustment = 4;                // 日期校正：-3到+3天的偏移量
}

// 批量日历查询请求
message BatchCalendarReq {
  repeated string year_months = 1;          // 年月列表，格式："YYYY-MM"，如："2025-05"
  string method_code = 2;                   // 计算方法：AUTO, LFNU, UMMUL_QURA
  int32 date_adjustment = 3;                // 日期校正：-3到+3天的偏移量
}

// 日历日期信息
message CalendarDateInfo {
  int32 gregorian_year = 1;                 // 公历年
  int32 gregorian_month = 2;                // 公历月
  int32 gregorian_day = 3;                  // 公历日
  int32 hijriah_year = 4;                   // Hijriah年
  int32 hijriah_month = 5;                  // Hijriah月 1-12 (Muharam, Safar, Rabiul Awal, Rabiul Akhir, Jumadal Ula, Jumadal Akhirah, Rajab, Sya'ban, Ramadhan, Syawal, Dzulqa'dah, Dzulhijjah)
  int32 hijriah_day = 6;                    // Hijriah日
  string method_code = 7;                   // 计算方法代码
  int32 weekday = 8;                        // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
  int32 pasaran = 9;                        // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
  string weekday_name = 10;                 // 星期名称（本地化）(Ahad Senin Selasa Rabu Kamis Jumat Sabtu)
  string pasaran_name = 11;                 // Pasaran名称（本地化）
  repeated CalendarEventInfo events = 12;   // 当日事件列表
}

// 日历事件信息
message CalendarEventInfo {
  int64 id = 1;                             // 事件ID
  string event_type = 2;                    // 事件类型：HARI_BESAR, LIBUR_NASIONAL, PUASA
  string title = 3;                         // 事件标题
  string description = 4;                   // 事件描述
  string jump_url = 5;                      // 点击跳转链接
}

// 日历数据
message CalendarData {
  repeated CalendarDateInfo list = 1;       // 日历数据列表
}

// 日历响应
message CalendarRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  CalendarData data = 4;
}

// 批量日历响应
message BatchCalendarRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  BatchCalendarData data = 4;
}

// 批量日历数据
message BatchCalendarData {
  map<string, CalendarData> calendars = 1;  // 日历数据映射，key为"YYYY-MM"格式，value为对应月份的日历数据
}

// 祷告时间查询请求
message GetDailyPrayerTimeReq {
  string date = 1;                        // 日期 YYYY-MM-DD 格式
  double latitude = 2;                    // 纬度
  double longitude = 3;                   // 经度
  string timezone = 4;                    // 时区，如 "Asia/Shanghai"
  string method_code = 5;                 // 计算方法：AUTO, LFNU, UMMUL_QURA (这个设置是在日历那边，用于返回伊斯兰日期)
  int32 date_adjustment = 6;              // 日期校正：-3到+3天的偏移量 (这个设置是在日历那边)
}

// 祷告时间查询响应
message GetDailyPrayerTimeRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  PrayerTimeData data = 4;
}

// 每日祷告时间数据
message PrayerTimeData {
  string date = 1;                        // 日期 YYYY-MM-DD 格式
  PrayerTime prayer_time = 2;             // 祷告时间
  IslamicDate islamic_date = 3;           // 伊斯兰历日期
}

// 祷告时间
message PrayerTime {
  string imsak = 1;                       // 伊姆萨克时间（仅斋月期间）
  string subuh = 2;                       // 晨祷时间
  string terbit = 3;                      // 日出时间
  string dhuha = 4;                       // 上午祷告时间（可选，目前还不准确）
  string zuhur = 5;                       // 晌祷时间
  string ashar = 6;                       // 晡祷时间
  string maghrib = 7;                     // 昏祷时间
  string isya = 8;                        // 宵祷时间
}

// 伊斯兰历日期
message IslamicDate {
  int32 year = 1;                         // 伊斯兰历年份
  int32 month = 2;                        // 伊斯兰历月份
  int32 day = 3;                          // 伊斯兰历日期
}

// 获取月度祷告时间请求
message GetMonthlyPrayerTimesReq {
  int32 year = 1;                         // 年份 YYYY 格式
  int32 month = 2;                        // 月份 1-12
  double latitude = 3;                    // 纬度
  double longitude = 4;                   // 经度
  string timezone = 5;                    // 时区，如 "Asia/Shanghai"
  string method_code = 6;                 // 计算方法：AUTO, LFNU, UMMUL_QURA
  int32 date_adjustment = 7;              // 日期校正：-3到+3天的偏移量
}

// 获取月度祷告时间响应
message GetMonthlyPrayerTimesRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  MonthlyPrayerTimesData data = 4;
}

// 月度祷告时间数据
message MonthlyPrayerTimesData {
  repeated PrayerTimeData list = 1;  // 每日祷告时间列表
}

// ==================== 朝觐日程相关接口 ====================

// 朝觐日程列表请求
message HajiJadwalListReq {}

// 朝觐日程详情请求
message HajiJadwalDetailReq {
  uint64 jadwal_id = 1;              // 朝觐日程ID
}

// 朝觐日程项目信息
message HajiJadwalInfo {
  uint64 id = 1;                     // 朝觐日程ID
  int32 item_no = 2;                 // 项目编号
  string time_info = 3;              // 时间信息
  string event_summary = 4;          // 事件简述
  string additional_info = 5;        // 附加信息（请求列表的时候返回空，详情的时候才有内容）
  string article_text = 6;           // 文章详情（副文本）
}

// 朝觐日程列表数据
message HajiJadwalListData {
  repeated HajiJadwalInfo list = 1;  // 朝觐日程列表
  string description = 2;            // 日程说明文字
  string year = 3;                   // 朝觐年份
}

// 朝觐日程列表响应
message HajiJadwalListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajiJadwalListData data = 4;
}

// 朝觐日程详情数据
message HajiJadwalDetailData {
  HajiJadwalInfo jadwal = 1;         // 朝觐日程详情
}

// 朝觐日程详情响应
message HajiJadwalDetailRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajiJadwalDetailData data = 4;
}

// ==================== 朝觐仪式顺序相关接口 ====================

// 朝觐仪式顺序列表请求
message HajiUrutanListReq {}

// 朝觐仪式顺序详情请求
message HajiUrutanDetailReq {
  uint64 urutan_id = 1;              // 朝觐仪式顺序ID
}

// 朝觐仪式顺序项目信息
message HajiUrutanInfo {
  uint64 id = 1;                     // 朝觐仪式顺序ID
  int32 urutan_no = 2;               // 仪式顺序编号
  string urutan_name = 3;            // 仪式名称
  string urutan_time = 4;            // 仪式时间信息
  string icon_url = 5;               // 图标URL
  string urutan_content = 6;         // 仪式内容描述（列表时为空，详情时才有内容）
}


// 朝觐仪式顺序列表数据
message HajiUrutanListData {
  repeated HajiUrutanInfo list = 1;  // 朝觐仪式顺序列表
}

// 朝觐仪式顺序列表响应
message HajiUrutanListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajiUrutanListData data = 4;
}

// 朝觐仪式顺序详情数据
message HajiUrutanDetailData {
  HajiUrutanInfo urutan = 1;         // 朝觐仪式顺序详情
}

// 朝觐仪式顺序详情响应
message HajiUrutanDetailRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajiUrutanDetailData data = 4;
}

// ==================== 副朝仪式顺序相关接口 ====================

// 副朝仪式顺序列表请求
message UmrahUrutanListReq {}

// 副朝仪式顺序详情请求
message UmrahUrutanDetailReq {
  uint64 urutan_id = 1;              // 副朝仪式顺序ID
}

// 副朝仪式顺序项目信息
message UmrahUrutanInfo {
  uint64 id = 1;                     // 副朝仪式顺序ID
  int32 urutan_no = 2;               // 仪式顺序编号
  string urutan_name = 3;            // 仪式名称
  string urutan_time = 4;            // 仪式时间信息
  string icon_url = 5;               // 图标URL
  string urutan_content = 6;         // 仪式内容描述（列表时为空，详情时才有内容）
}

// 副朝仪式顺序列表数据
message UmrahUrutanListData {
  repeated UmrahUrutanInfo list = 1;  // 副朝仪式顺序列表
}

// 副朝仪式顺序列表响应
message UmrahUrutanListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UmrahUrutanListData data = 4;
}

// 副朝仪式顺序详情数据
message UmrahUrutanDetailData {
  UmrahUrutanInfo urutan = 1;         // 副朝仪式顺序详情
}

// 副朝仪式顺序详情响应
message UmrahUrutanDetailRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UmrahUrutanDetailData data = 4;
}

// ==================== 朝觐祈祷文相关接口 ====================

// 朝觐祈祷文简要列表请求（一次过返回所有数据）
message HajiDoaRingkasListReq {}

// 朝觐祈祷文内容信息
message HajiDoaRingkasContentInfo {
  uint64 id = 1;                     // 内容ID
  int32 content_order = 2;           // 内容排序
  string title = 3;                  // 内容标题（可为空）
  string muqatta_at = 4;             // Muqattaʿāt断章字母（有则展示，无不展示）
  string arabic_text = 5;            // 阿拉伯文原文
  string indonesian_text = 6;        // 印尼语翻译
  string latin_text = 7;             // 拉丁音译文本
}

// 朝觐祈祷文简要信息
message HajiDoaRingkasInfo {
  uint64 id = 1;                     // 祈祷文ID
  int32 doa_no = 2;                  // 祈祷文序号
  string doa_name = 3;               // 祈祷文名称
  repeated HajiDoaRingkasContentInfo contents = 4;  // 祈祷文内容列表
}

// 朝觐祈祷文简要列表数据
message HajiDoaRingkasListData {
  repeated HajiDoaRingkasInfo list = 1;  // 祈祷文简要列表
}

// 朝觐祈祷文简要列表响应
message HajiDoaRingkasListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajiDoaRingkasListData data = 4;
}

// 朝觐祈祷文详细列表请求
message HajiDoaPanjangListReq {}

// 朝觐祈祷文详细信息
message HajiDoaPanjangInfo {
  uint64 id = 1;                     // 祈祷文ID
  int32 doa_no = 2;                  // 祈祷文序号
  string doa_name = 3;               // 祈祷文名称
  int32 bacaan_count = 4;            // 诵读数
}

// 朝觐祈祷文详细列表数据
message HajiDoaPanjangListData {
  repeated HajiDoaPanjangInfo list = 1;  // 祈祷文详细列表
}

// 朝觐祈祷文详细列表响应
message HajiDoaPanjangListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajiDoaPanjangListData data = 4;
}

// 朝觐祈祷文诵读内容请求
message HajiDoaPanjangBacaanListReq {
  uint64 doa_id = 1;                 // 祈祷文ID
}

// 朝觐祈祷文诵读内容信息
message HajiDoaPanjangBacaanContentInfo {
  uint64 id = 1;                     // 内容ID
  int32 content_order = 2;           // 内容排序
  string title = 3;                  // 内容标题（可为空）
  string muqatta_at = 4;             // Muqattaʿāt断章字母（有则展示，无不展示）
  string arabic_text = 5;            // 阿拉伯文原文
  string indonesian_text = 6;        // 印尼语翻译
  string latin_text = 7;             // 拉丁音译文本
}

// 朝觐祈祷文诵读信息
message HajiDoaPanjangBacaanInfo {
  uint64 id = 1;                     // 诵读ID
  uint64 doa_id = 2;                 // 祈祷文ID
  int32 bacaan_no = 3;               // 诵读序号
  string bacaan_name = 4;            // 诵读名称
  repeated HajiDoaPanjangBacaanContentInfo contents = 5;  // 诵读内容列表
}

// 朝觐祈祷文诵读内容列表数据
message HajiDoaPanjangBacaanListData {
  repeated HajiDoaPanjangBacaanInfo list = 1;   // 诵读内容列表
  int32 doa_no = 2;                      // 所属祈祷文序号
  string doa_name = 3;                   // 所属祈祷文名称
}

// 朝觐祈祷文诵读内容列表响应
message HajiDoaPanjangBacaanListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajiDoaPanjangBacaanListData data = 4;
}

// ==================== 副朝祈祷文相关接口 ====================

// 副朝祈祷文简要列表请求
message UmrahDoaRingkasListReq {}

// 副朝祈祷文内容信息
message UmrahDoaRingkasContentInfo {
  uint64 id = 1;                     // 内容ID
  int32 content_order = 2;           // 内容排序
  string title = 3;                  // 内容标题（可为空）
  string muqatta_at = 4;             // Muqattaʿāt断章字母（有则展示，无不展示）
  string arabic_text = 5;            // 阿拉伯文原文
  string indonesian_text = 6;        // 印尼语翻译
  string latin_text = 7;             // 拉丁音译文本
}

// 副朝祈祷文简要信息
message UmrahDoaRingkasInfo {
  uint64 id = 1;                     // 祈祷文ID
  int32 doa_no = 2;                  // 祈祷文序号
  string doa_name = 3;               // 祈祷文名称
  repeated UmrahDoaRingkasContentInfo contents = 4;  // 祈祷文内容列表
}

// 副朝祈祷文简要列表数据
message UmrahDoaRingkasListData {
  repeated UmrahDoaRingkasInfo list = 1;  // 祈祷文简要列表
}

// 副朝祈祷文简要列表响应
message UmrahDoaRingkasListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UmrahDoaRingkasListData data = 4;
}

// 副朝祈祷文详细列表请求
message UmrahDoaPanjangListReq {}

// 副朝祈祷文详细信息
message UmrahDoaPanjangInfo {
  uint64 id = 1;                     // 祈祷文ID
  int32 doa_no = 2;                  // 祈祷文序号
  string doa_name = 3;               // 祈祷文名称
  int32 bacaan_count = 4;            // 诵读数
}

// 副朝祈祷文详细列表数据
message UmrahDoaPanjangListData {
  repeated UmrahDoaPanjangInfo list = 1;  // 祈祷文详细列表
}

// 副朝祈祷文详细列表响应
message UmrahDoaPanjangListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UmrahDoaPanjangListData data = 4;
}

// 副朝祈祷文诵读内容请求
message UmrahDoaPanjangBacaanListReq {
  uint64 doa_id = 1;                 // 祈祷文ID
}

// 副朝祈祷文诵读内容信息
message UmrahDoaPanjangBacaanContentInfo {
  uint64 id = 1;                     // 内容ID
  int32 content_order = 2;           // 内容排序
  string title = 3;                  // 内容标题（可为空）
  string muqatta_at = 4;             // Muqattaʿāt断章字母（有则展示，无不展示）
  string arabic_text = 5;            // 阿拉伯文原文
  string indonesian_text = 6;        // 印尼语翻译
  string latin_text = 7;             // 拉丁音译文本
}

// 副朝祈祷文诵读信息
message UmrahDoaPanjangBacaanInfo {
  uint64 id = 1;                     // 诵读ID
  uint64 doa_id = 2;                 // 祈祷文ID
  int32 bacaan_no = 3;               // 诵读序号
  string bacaan_name = 4;            // 诵读名称
  repeated UmrahDoaPanjangBacaanContentInfo contents = 5;  // 诵读内容列表
}

// 副朝祈祷文诵读内容列表数据
message UmrahDoaPanjangBacaanListData {
  repeated UmrahDoaPanjangBacaanInfo list = 1;   // 诵读内容列表
  int32 doa_no = 2;                      // 所属祈祷文序号
  string doa_name = 3;                   // 所属祈祷文名称
}

// 副朝祈祷文诵读内容列表响应
message UmrahDoaPanjangBacaanListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UmrahDoaPanjangBacaanListData data = 4;
}

// ==================== 朝觐智慧相关接口 ====================

// 朝觐智慧列表请求
message HajiHikmahListReq {
}

// 朝觐智慧信息
message HajiHikmahInfo {
  uint64 id = 1;                       // 朝觐智慧ID
  uint64 article_id = 2;               // 文章ID
  string title = 3;                    // 标题
}

// 朝觐智慧列表数据
message HajiHikmahListData {
  repeated HajiHikmahInfo list = 1;    // 朝觐智慧列表
}

// 朝觐智慧列表响应
message HajiHikmahListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajiHikmahListData data = 4;
}

// ==================== 副朝智慧相关接口 ====================

// 副朝智慧列表请求
message UmrahHikmahListReq {
}

// 副朝智慧信息
message UmrahHikmahInfo {
  uint64 id = 1;                       // 副朝智慧ID
  uint64 article_id = 2;               // 文章ID
  string title = 3;                    // 标题
}

// 副朝智慧列表数据
message UmrahHikmahListData {
  repeated UmrahHikmahInfo list = 1;   // 副朝智慧列表
}

// 副朝智慧列表响应
message UmrahHikmahListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UmrahHikmahListData data = 4;
}

// ==================== 朝觐地标相关接口 ====================

// 朝觐地标列表请求
message HajiLandmarkListReq {
  string inner_type = 1;              // 内部类型: destinasi(目的地), tokoh(人物)
  common.PageRequest page = 2;        // 分页参数
}

// 朝觐地标信息（列表时返回）
message HajiLandmarkItem {
  uint64 landmark_id = 1;             // 地标ID
  string type_icon_url = 2;           // 类型图标URL
  string type_name = 3;               // 类型名称
  double latitude = 4;                // 纬度
  double longitude = 5;               // 经度
  string image_url = 6;               // 图片URL
  string landmark_name = 7;           // 地标名称
}

// 朝觐地标信息（详情时返回）
message HajiLandmarkInfo {
  uint64 landmark_id = 1;             // 地标ID
  string type_icon_url = 2;           // 类型图标URL
  string type_name = 3;               // 类型名称
  double latitude = 4;                // 纬度
  double longitude = 5;               // 经度
  string image_url = 6;               // 图片URL
  string landmark_name = 7;           // 地标名称
  string country = 8;                 // 国家/地区
  string address = 9;                 // 详细地址
  string short_description = 10;      // 简介
  string information_text = 11;       // 详细介绍
}

// 朝觐地标列表数据
message HajiLandmarkListData {
  repeated HajiLandmarkItem list = 1;   // 地标列表
  common.PageResponse page = 2;        // 分页响应
}

// 朝觐地标列表响应
message HajiLandmarkListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajiLandmarkListData data = 4;
}

// 朝觐地标详情请求
message HajiLandmarkDetailReq {
  uint64 landmark_id = 1;             // 地标ID
}

// 朝觐地标详情数据
message HajiLandmarkDetailData {
  HajiLandmarkInfo landmark = 1;      // 地标详情信息
}

// 朝觐地标详情响应
message HajiLandmarkDetailRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajiLandmarkDetailData data = 4;
}

// ==================== 副朝地标相关接口 ====================

// 副朝地标列表请求
message UmrahLandmarkListReq {
  string inner_type = 1;              // 内部类型: destinasi(目的地), tokoh(人物)
  common.PageRequest page = 2;        // 分页参数
}

// 副朝地标信息（列表时返回）
message UmrahLandmarkItem {
  uint64 landmark_id = 1;             // 地标ID
  string type_icon_url = 2;           // 类型图标URL
  string type_name = 3;               // 类型名称
  double latitude = 4;                // 纬度
  double longitude = 5;               // 经度
  string image_url = 6;               // 图片URL
  string landmark_name = 7;           // 地标名称
}

// 副朝地标信息（详情时返回）
message UmrahLandmarkInfo {
  uint64 landmark_id = 1;             // 地标ID
  string type_icon_url = 2;           // 类型图标URL
  string type_name = 3;               // 类型名称
  double latitude = 4;                // 纬度
  double longitude = 5;               // 经度
  string image_url = 6;               // 图片URL
  string landmark_name = 7;           // 地标名称
  string country = 8;                 // 国家/地区
  string address = 9;                 // 详细地址
  string short_description = 10;      // 简介
  string information_text = 11;       // 详细介绍
}

// 副朝地标列表数据
message UmrahLandmarkListData {
  repeated UmrahLandmarkItem list = 1;   // 地标列表
  common.PageResponse page = 2;        // 分页响应
}

// 副朝地标列表响应
message UmrahLandmarkListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UmrahLandmarkListData data = 4;
}

// 副朝地标详情请求
message UmrahLandmarkDetailReq {
  uint64 landmark_id = 1;             // 地标ID
}

// 副朝地标详情数据
message UmrahLandmarkDetailData {
  UmrahLandmarkInfo landmark = 1;      // 地标详情信息
}

// 副朝地标详情响应
message UmrahLandmarkDetailRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UmrahLandmarkDetailData data = 4;
}


  // ==================== 朝觐新闻相关接口 ====================

  // 朝觐新闻列表请求
  message HajiNewsListReq {
    common.PageRequest page = 1;        // 分页参数
  }

  // 新闻列表项
  message HajiNewsItem {
    uint64 article_id = 1;              // 文章ID
    string category_name = 2;           // 资讯分类名称
    string title = 3;                   // 标题
    int64 publish_time = 4;             // 发布时间（毫秒时间戳）
    string cover_image = 5;             // 封面图片URL
  }

  // 朝觐新闻列表数据
  message HajiNewsListData {
    repeated HajiNewsItem list = 1;     // 新闻列表
    common.PageResponse page = 2;       // 分页信息
    string header_tag = 3;              // sort_order 最靠前的标签名
  }

  // 朝觐新闻列表响应
  message HajiNewsListRes {
    int32 code = 1;
    string msg = 2;
    common.Error error = 3;
    HajiNewsListData data = 4;
  }

// 祷告时间服务定义
service PrayerService {
  // 获取日历数据
  rpc GetCalendar(CalendarReq) returns (CalendarRes);

  // 批量获取多个年月的日历数据
  rpc GetBatchCalendar(BatchCalendarReq) returns (BatchCalendarRes);

  // 获取每天祷告时间（前端自己选择用哪个吧）
  rpc GetDailyPrayerTime(GetDailyPrayerTimeReq) returns (GetDailyPrayerTimeRes);

  // 获取月度祷告时间（前端自己选择用哪个吧）
  rpc GetMonthlyPrayerTimes(GetMonthlyPrayerTimesReq) returns (GetMonthlyPrayerTimesRes);

  // ==================== 朝觐日程相关接口 ====================

  // 获取朝觐日程列表
  rpc GetHajiJadwalList(HajiJadwalListReq) returns (HajiJadwalListRes);

  // 获取朝觐日程详情
  rpc GetHajiJadwalDetail(HajiJadwalDetailReq) returns (HajiJadwalDetailRes);

  // ==================== 朝觐仪式相关接口 ====================

  // 获取朝觐仪式顺序列表
  rpc GetHajiUrutanList(HajiUrutanListReq) returns (HajiUrutanListRes);

  // 获取朝觐仪式顺序详情
  rpc GetHajiUrutanDetail(HajiUrutanDetailReq) returns (HajiUrutanDetailRes);

  // ==================== 朝觐祈祷文相关接口 ====================

  // 获取朝觐祈祷文简要列表
  rpc GetHajiDoaRingkasList(HajiDoaRingkasListReq) returns (HajiDoaRingkasListRes);

  // 获取朝觐祈祷文详细列表
  rpc GetHajiDoaPanjangList(HajiDoaPanjangListReq) returns (HajiDoaPanjangListRes);

  // 获取朝觐祈祷文诵读内容列表
  rpc GetHajiDoaPanjangBacaanList(HajiDoaPanjangBacaanListReq) returns (HajiDoaPanjangBacaanListRes);

  // ==================== 朝觐智慧相关接口 ====================

  // 获取朝觐智慧列表
  rpc GetHajiHikmahList(HajiHikmahListReq) returns (HajiHikmahListRes);


  // ==================== 朝觐新闻相关接口 ====================

  // 获取朝觐新闻列表
  rpc GetHajiNewsList(HajiNewsListReq) returns (HajiNewsListRes);


  // ==================== 朝觐地标相关接口 ====================

  // 获取朝觐地标列表
  rpc GetHajiLandmarkList(HajiLandmarkListReq) returns (HajiLandmarkListRes);

  // 获取朝觐地标详情
  rpc GetHajiLandmarkDetail(HajiLandmarkDetailReq) returns (HajiLandmarkDetailRes);

  // ==================== 副朝仪式相关接口 ====================

  // 获取副朝仪式顺序列表
  rpc GetUmrahUrutanList(UmrahUrutanListReq) returns (UmrahUrutanListRes);

  // 获取副朝仪式顺序详情
  rpc GetUmrahUrutanDetail(UmrahUrutanDetailReq) returns (UmrahUrutanDetailRes);

  // ==================== 副朝祈祷文相关接口 ====================

  // 获取副朝祈祷文简要列表
  rpc GetUmrahDoaRingkasList(UmrahDoaRingkasListReq) returns (UmrahDoaRingkasListRes);

  // 获取副朝祈祷文详细列表
  rpc GetUmrahDoaPanjangList(UmrahDoaPanjangListReq) returns (UmrahDoaPanjangListRes);

  // 获取副朝祈祷文诵读内容列表
  rpc GetUmrahDoaPanjangBacaanList(UmrahDoaPanjangBacaanListReq) returns (UmrahDoaPanjangBacaanListRes);

  // ==================== 副朝智慧相关接口 ====================

  // 获取副朝智慧列表
  rpc GetUmrahHikmahList(UmrahHikmahListReq) returns (UmrahHikmahListRes);

  // ==================== 副朝地标相关接口 ====================

  // 获取副朝地标列表
  rpc GetUmrahLandmarkList(UmrahLandmarkListReq) returns (UmrahLandmarkListRes);

  // 获取副朝地标详情
  rpc GetUmrahLandmarkDetail(UmrahLandmarkDetailReq) returns (UmrahLandmarkDetailRes);
}
