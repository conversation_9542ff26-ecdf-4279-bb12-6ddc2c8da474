// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: islamic/v1/wisdow.proto

package islamicv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 名言标签列表请求
type WisdomCateReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WisdomCateReq) Reset() {
	*x = WisdomCateReq{}
	mi := &file_islamic_v1_wisdow_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomCateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomCateReq) ProtoMessage() {}

func (x *WisdomCateReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_wisdow_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomCateReq.ProtoReflect.Descriptor instead.
func (*WisdomCateReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_wisdow_proto_rawDescGZIP(), []int{0}
}

func (x *WisdomCateReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

// 名言标签列表数据
type WisdomCateListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*WisdomCateItem      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WisdomCateListData) Reset() {
	*x = WisdomCateListData{}
	mi := &file_islamic_v1_wisdow_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomCateListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomCateListData) ProtoMessage() {}

func (x *WisdomCateListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_wisdow_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomCateListData.ProtoReflect.Descriptor instead.
func (*WisdomCateListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_wisdow_proto_rawDescGZIP(), []int{1}
}

func (x *WisdomCateListData) GetList() []*WisdomCateItem {
	if x != nil {
		return x.List
	}
	return nil
}

// 名言标签列表响应
type WisdomCateRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *WisdomCateListData    `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty" dc:"名言标签列表"` // 名言标签列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WisdomCateRes) Reset() {
	*x = WisdomCateRes{}
	mi := &file_islamic_v1_wisdow_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomCateRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomCateRes) ProtoMessage() {}

func (x *WisdomCateRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_wisdow_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomCateRes.ProtoReflect.Descriptor instead.
func (*WisdomCateRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_wisdow_proto_rawDescGZIP(), []int{2}
}

func (x *WisdomCateRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WisdomCateRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *WisdomCateRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *WisdomCateRes) GetData() *WisdomCateListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 名言标签item
type WisdomCateItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"分类ID"`                                   // 分类ID
	IsOpen        int32                  `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty" dc:"是否启用"`             // 是否启用
	Sort          int32                  `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty" dc:"排序"`                                 // 排序
	CateCount     int32                  `protobuf:"varint,4,opt,name=cate_count,json=cateCount,proto3" json:"cate_count,omitempty" dc:"该分类下的数量"` // 该分类下的数量
	Title         string                 `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty" dc:"分类名称"`                              // 分类名称
	LanguageId    uint32                 `protobuf:"varint,6,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WisdomCateItem) Reset() {
	*x = WisdomCateItem{}
	mi := &file_islamic_v1_wisdow_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomCateItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomCateItem) ProtoMessage() {}

func (x *WisdomCateItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_wisdow_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomCateItem.ProtoReflect.Descriptor instead.
func (*WisdomCateItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_wisdow_proto_rawDescGZIP(), []int{3}
}

func (x *WisdomCateItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WisdomCateItem) GetIsOpen() int32 {
	if x != nil {
		return x.IsOpen
	}
	return 0
}

func (x *WisdomCateItem) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *WisdomCateItem) GetCateCount() int32 {
	if x != nil {
		return x.CateCount
	}
	return 0
}

func (x *WisdomCateItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *WisdomCateItem) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

// 名言列表请求
type WisdomListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
	CateId        uint32                 `protobuf:"varint,2,opt,name=cate_id,json=cateId,proto3" json:"cate_id,omitempty" dc:"分类id"`             // 分类id
	Keyword       string                 `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty" dc:"搜索关键字"`                         // 搜索关键字
	Page          *common.PageRequest    `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WisdomListReq) Reset() {
	*x = WisdomListReq{}
	mi := &file_islamic_v1_wisdow_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomListReq) ProtoMessage() {}

func (x *WisdomListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_wisdow_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomListReq.ProtoReflect.Descriptor instead.
func (*WisdomListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_wisdow_proto_rawDescGZIP(), []int{4}
}

func (x *WisdomListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *WisdomListReq) GetCateId() uint32 {
	if x != nil {
		return x.CateId
	}
	return 0
}

func (x *WisdomListReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *WisdomListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type WisdomListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*WisdomListItem      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WisdomListData) Reset() {
	*x = WisdomListData{}
	mi := &file_islamic_v1_wisdow_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomListData) ProtoMessage() {}

func (x *WisdomListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_wisdow_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomListData.ProtoReflect.Descriptor instead.
func (*WisdomListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_wisdow_proto_rawDescGZIP(), []int{5}
}

func (x *WisdomListData) GetList() []*WisdomListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *WisdomListData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 名言列表响应
type WisdomListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *WisdomListData        `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty" dc:"名言列表"` // 名言列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WisdomListRes) Reset() {
	*x = WisdomListRes{}
	mi := &file_islamic_v1_wisdow_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomListRes) ProtoMessage() {}

func (x *WisdomListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_wisdow_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomListRes.ProtoReflect.Descriptor instead.
func (*WisdomListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_wisdow_proto_rawDescGZIP(), []int{6}
}

func (x *WisdomListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WisdomListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *WisdomListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *WisdomListRes) GetData() *WisdomListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 名言item
type WisdomListItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"问题ID"`                                           // 问题ID
	IsOpen        int32                  `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty" dc:"是否启用"`                     // 是否启用
	Sort          int32                  `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty" dc:"排序"`                                         // 排序
	Views         int32                  `protobuf:"varint,4,opt,name=views,proto3" json:"views,omitempty" dc:"浏览次数"`                                     // 浏览次数
	Title         string                 `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty" dc:"图片标题"`                                      // 图片标题
	Desc          string                 `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty" dc:"问题描述"`                                        // 问题描述
	LanguageId    int32                  `protobuf:"varint,7,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"`         // 语言id
	WisdomCateId  uint32                 `protobuf:"varint,8,opt,name=wisdom_cate_id,json=wisdomCateId,proto3" json:"wisdom_cate_id,omitempty" dc:"分类id"` // 分类id
	ImageUrl      string                 `protobuf:"bytes,9,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty" dc:"图片"`                  // 图片
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WisdomListItem) Reset() {
	*x = WisdomListItem{}
	mi := &file_islamic_v1_wisdow_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomListItem) ProtoMessage() {}

func (x *WisdomListItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_wisdow_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomListItem.ProtoReflect.Descriptor instead.
func (*WisdomListItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_wisdow_proto_rawDescGZIP(), []int{7}
}

func (x *WisdomListItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WisdomListItem) GetIsOpen() int32 {
	if x != nil {
		return x.IsOpen
	}
	return 0
}

func (x *WisdomListItem) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *WisdomListItem) GetViews() int32 {
	if x != nil {
		return x.Views
	}
	return 0
}

func (x *WisdomListItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *WisdomListItem) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *WisdomListItem) GetLanguageId() int32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *WisdomListItem) GetWisdomCateId() uint32 {
	if x != nil {
		return x.WisdomCateId
	}
	return 0
}

func (x *WisdomListItem) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

// 名言详情请求
type WisdomOneReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
	Id            uint32                 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty" dc:"id"`                                     // id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WisdomOneReq) Reset() {
	*x = WisdomOneReq{}
	mi := &file_islamic_v1_wisdow_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomOneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomOneReq) ProtoMessage() {}

func (x *WisdomOneReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_wisdow_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomOneReq.ProtoReflect.Descriptor instead.
func (*WisdomOneReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_wisdow_proto_rawDescGZIP(), []int{8}
}

func (x *WisdomOneReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *WisdomOneReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 名言详情响应
type WisdomOneRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *WisdomOneItem         `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WisdomOneRes) Reset() {
	*x = WisdomOneRes{}
	mi := &file_islamic_v1_wisdow_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomOneRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomOneRes) ProtoMessage() {}

func (x *WisdomOneRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_wisdow_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomOneRes.ProtoReflect.Descriptor instead.
func (*WisdomOneRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_wisdow_proto_rawDescGZIP(), []int{9}
}

func (x *WisdomOneRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WisdomOneRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *WisdomOneRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *WisdomOneRes) GetData() *WisdomOneItem {
	if x != nil {
		return x.Data
	}
	return nil
}

// 名言详情item
type WisdomOneItem struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ArticleId        uint32                 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty" dc:"文章id"`                         // 文章id
	LanguageId       uint32                 `protobuf:"varint,2,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"`                      // 语言id
	Name             string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" dc:"文章标题"`                                                     // 文章标题
	Content          string                 `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty" dc:"文章内容"`                                               // 文章内容
	CategoryId       uint32                 `protobuf:"varint,5,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类id"`                      // 分类id
	CategoryName     string                 `protobuf:"bytes,6,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty" dc:"分类名称"`                 // 分类名称
	CoverImgs        string                 `protobuf:"bytes,7,opt,name=cover_imgs,json=coverImgs,proto3" json:"cover_imgs,omitempty" dc:"专题图片"`                          // 专题图片
	Author           string                 `protobuf:"bytes,8,opt,name=author,proto3" json:"author,omitempty" dc:"创建人"`                                                  // 创建人
	PublishTime      int64                  `protobuf:"varint,9,opt,name=publish_time,json=publishTime,proto3" json:"publish_time,omitempty" dc:"发布时间"`                   // 发布时间
	AuthorLogo       string                 `protobuf:"bytes,10,opt,name=author_logo,json=authorLogo,proto3" json:"author_logo,omitempty" dc:"发布时间"`                      // 发布时间
	AuthorAuthStatus uint32                 `protobuf:"varint,11,opt,name=author_auth_status,json=authorAuthStatus,proto3" json:"author_auth_status,omitempty" dc:"发布时间"` // 发布时间
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *WisdomOneItem) Reset() {
	*x = WisdomOneItem{}
	mi := &file_islamic_v1_wisdow_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomOneItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomOneItem) ProtoMessage() {}

func (x *WisdomOneItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_wisdow_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomOneItem.ProtoReflect.Descriptor instead.
func (*WisdomOneItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_wisdow_proto_rawDescGZIP(), []int{10}
}

func (x *WisdomOneItem) GetArticleId() uint32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *WisdomOneItem) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *WisdomOneItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WisdomOneItem) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *WisdomOneItem) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *WisdomOneItem) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *WisdomOneItem) GetCoverImgs() string {
	if x != nil {
		return x.CoverImgs
	}
	return ""
}

func (x *WisdomOneItem) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *WisdomOneItem) GetPublishTime() int64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

func (x *WisdomOneItem) GetAuthorLogo() string {
	if x != nil {
		return x.AuthorLogo
	}
	return ""
}

func (x *WisdomOneItem) GetAuthorAuthStatus() uint32 {
	if x != nil {
		return x.AuthorAuthStatus
	}
	return 0
}

var File_islamic_v1_wisdow_proto protoreflect.FileDescriptor

const file_islamic_v1_wisdow_proto_rawDesc = "" +
	"\n" +
	"\x17islamic/v1/wisdow.proto\x12\n" +
	"islamic.v1\x1a\x11common/base.proto\x1a\x1egoogle/protobuf/wrappers.proto\"0\n" +
	"\rWisdomCateReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\"D\n" +
	"\x12WisdomCateListData\x12.\n" +
	"\x04list\x18\x01 \x03(\v2\x1a.islamic.v1.WisdomCateItemR\x04list\"\x8e\x01\n" +
	"\rWisdomCateRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x122\n" +
	"\x04data\x18\x04 \x01(\v2\x1e.islamic.v1.WisdomCateListDataR\x04data\"\xa3\x01\n" +
	"\x0eWisdomCateItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x17\n" +
	"\ais_open\x18\x02 \x01(\x05R\x06isOpen\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\x12\x1d\n" +
	"\n" +
	"cate_count\x18\x04 \x01(\x05R\tcateCount\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12\x1f\n" +
	"\vlanguage_id\x18\x06 \x01(\rR\n" +
	"languageId\"\x8c\x01\n" +
	"\rWisdomListReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\x12\x17\n" +
	"\acate_id\x18\x02 \x01(\rR\x06cateId\x12\x18\n" +
	"\akeyword\x18\x03 \x01(\tR\akeyword\x12'\n" +
	"\x04page\x18\x04 \x01(\v2\x13.common.PageRequestR\x04page\"j\n" +
	"\x0eWisdomListData\x12.\n" +
	"\x04list\x18\x01 \x03(\v2\x1a.islamic.v1.WisdomListItemR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x8a\x01\n" +
	"\rWisdomListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12.\n" +
	"\x04data\x18\x04 \x01(\v2\x1a.islamic.v1.WisdomListDataR\x04data\"\xf1\x01\n" +
	"\x0eWisdomListItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x17\n" +
	"\ais_open\x18\x02 \x01(\x05R\x06isOpen\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\x12\x14\n" +
	"\x05views\x18\x04 \x01(\x05R\x05views\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12\x12\n" +
	"\x04desc\x18\x06 \x01(\tR\x04desc\x12\x1f\n" +
	"\vlanguage_id\x18\a \x01(\x05R\n" +
	"languageId\x12$\n" +
	"\x0ewisdom_cate_id\x18\b \x01(\rR\fwisdomCateId\x12\x1b\n" +
	"\timage_url\x18\t \x01(\tR\bimageUrl\"?\n" +
	"\fWisdomOneReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\rR\x02id\"\x88\x01\n" +
	"\fWisdomOneRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12-\n" +
	"\x04data\x18\x05 \x01(\v2\x19.islamic.v1.WisdomOneItemR\x04data\"\xec\x02\n" +
	"\rWisdomOneItem\x12\x1d\n" +
	"\n" +
	"article_id\x18\x01 \x01(\rR\tarticleId\x12\x1f\n" +
	"\vlanguage_id\x18\x02 \x01(\rR\n" +
	"languageId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x18\n" +
	"\acontent\x18\x04 \x01(\tR\acontent\x12\x1f\n" +
	"\vcategory_id\x18\x05 \x01(\rR\n" +
	"categoryId\x12#\n" +
	"\rcategory_name\x18\x06 \x01(\tR\fcategoryName\x12\x1d\n" +
	"\n" +
	"cover_imgs\x18\a \x01(\tR\tcoverImgs\x12\x16\n" +
	"\x06author\x18\b \x01(\tR\x06author\x12!\n" +
	"\fpublish_time\x18\t \x01(\x03R\vpublishTime\x12\x1f\n" +
	"\vauthor_logo\x18\n" +
	" \x01(\tR\n" +
	"authorLogo\x12,\n" +
	"\x12author_auth_status\x18\v \x01(\rR\x10authorAuthStatus2\xdc\x01\n" +
	"\rWisdomService\x12F\n" +
	"\x0eWisdomCateList\x12\x19.islamic.v1.WisdomCateReq\x1a\x19.islamic.v1.WisdomCateRes\x12B\n" +
	"\n" +
	"WisdomList\x12\x19.islamic.v1.WisdomListReq\x1a\x19.islamic.v1.WisdomListRes\x12?\n" +
	"\tWisdomOne\x12\x18.islamic.v1.WisdomOneReq\x1a\x18.islamic.v1.WisdomOneResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_wisdow_proto_rawDescOnce sync.Once
	file_islamic_v1_wisdow_proto_rawDescData []byte
)

func file_islamic_v1_wisdow_proto_rawDescGZIP() []byte {
	file_islamic_v1_wisdow_proto_rawDescOnce.Do(func() {
		file_islamic_v1_wisdow_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_wisdow_proto_rawDesc), len(file_islamic_v1_wisdow_proto_rawDesc)))
	})
	return file_islamic_v1_wisdow_proto_rawDescData
}

var file_islamic_v1_wisdow_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_islamic_v1_wisdow_proto_goTypes = []any{
	(*WisdomCateReq)(nil),       // 0: islamic.v1.WisdomCateReq
	(*WisdomCateListData)(nil),  // 1: islamic.v1.WisdomCateListData
	(*WisdomCateRes)(nil),       // 2: islamic.v1.WisdomCateRes
	(*WisdomCateItem)(nil),      // 3: islamic.v1.WisdomCateItem
	(*WisdomListReq)(nil),       // 4: islamic.v1.WisdomListReq
	(*WisdomListData)(nil),      // 5: islamic.v1.WisdomListData
	(*WisdomListRes)(nil),       // 6: islamic.v1.WisdomListRes
	(*WisdomListItem)(nil),      // 7: islamic.v1.WisdomListItem
	(*WisdomOneReq)(nil),        // 8: islamic.v1.WisdomOneReq
	(*WisdomOneRes)(nil),        // 9: islamic.v1.WisdomOneRes
	(*WisdomOneItem)(nil),       // 10: islamic.v1.WisdomOneItem
	(*common.Error)(nil),        // 11: common.Error
	(*common.PageRequest)(nil),  // 12: common.PageRequest
	(*common.PageResponse)(nil), // 13: common.PageResponse
}
var file_islamic_v1_wisdow_proto_depIdxs = []int32{
	3,  // 0: islamic.v1.WisdomCateListData.list:type_name -> islamic.v1.WisdomCateItem
	11, // 1: islamic.v1.WisdomCateRes.error:type_name -> common.Error
	1,  // 2: islamic.v1.WisdomCateRes.data:type_name -> islamic.v1.WisdomCateListData
	12, // 3: islamic.v1.WisdomListReq.page:type_name -> common.PageRequest
	7,  // 4: islamic.v1.WisdomListData.list:type_name -> islamic.v1.WisdomListItem
	13, // 5: islamic.v1.WisdomListData.page:type_name -> common.PageResponse
	11, // 6: islamic.v1.WisdomListRes.error:type_name -> common.Error
	5,  // 7: islamic.v1.WisdomListRes.data:type_name -> islamic.v1.WisdomListData
	11, // 8: islamic.v1.WisdomOneRes.error:type_name -> common.Error
	10, // 9: islamic.v1.WisdomOneRes.data:type_name -> islamic.v1.WisdomOneItem
	0,  // 10: islamic.v1.WisdomService.WisdomCateList:input_type -> islamic.v1.WisdomCateReq
	4,  // 11: islamic.v1.WisdomService.WisdomList:input_type -> islamic.v1.WisdomListReq
	8,  // 12: islamic.v1.WisdomService.WisdomOne:input_type -> islamic.v1.WisdomOneReq
	2,  // 13: islamic.v1.WisdomService.WisdomCateList:output_type -> islamic.v1.WisdomCateRes
	6,  // 14: islamic.v1.WisdomService.WisdomList:output_type -> islamic.v1.WisdomListRes
	9,  // 15: islamic.v1.WisdomService.WisdomOne:output_type -> islamic.v1.WisdomOneRes
	13, // [13:16] is the sub-list for method output_type
	10, // [10:13] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_islamic_v1_wisdow_proto_init() }
func file_islamic_v1_wisdow_proto_init() {
	if File_islamic_v1_wisdow_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_wisdow_proto_rawDesc), len(file_islamic_v1_wisdow_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_wisdow_proto_goTypes,
		DependencyIndexes: file_islamic_v1_wisdow_proto_depIdxs,
		MessageInfos:      file_islamic_v1_wisdow_proto_msgTypes,
	}.Build()
	File_islamic_v1_wisdow_proto = out.File
	file_islamic_v1_wisdow_proto_goTypes = nil
	file_islamic_v1_wisdow_proto_depIdxs = nil
}
