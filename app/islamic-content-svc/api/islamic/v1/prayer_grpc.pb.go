// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.27.2
// source: islamic/v1/prayer.proto

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PrayerService_GetCalendar_FullMethodName                  = "/islamic.v1.PrayerService/GetCalendar"
	PrayerService_GetBatchCalendar_FullMethodName             = "/islamic.v1.PrayerService/GetBatchCalendar"
	PrayerService_GetDailyPrayerTime_FullMethodName           = "/islamic.v1.PrayerService/GetDailyPrayerTime"
	PrayerService_GetMonthlyPrayerTimes_FullMethodName        = "/islamic.v1.PrayerService/GetMonthlyPrayerTimes"
	PrayerService_GetHajiJadwalList_FullMethodName            = "/islamic.v1.PrayerService/GetHajiJadwalList"
	PrayerService_GetHajiJadwalDetail_FullMethodName          = "/islamic.v1.PrayerService/GetHajiJadwalDetail"
	PrayerService_GetHajiUrutanList_FullMethodName            = "/islamic.v1.PrayerService/GetHajiUrutanList"
	PrayerService_GetHajiUrutanDetail_FullMethodName          = "/islamic.v1.PrayerService/GetHajiUrutanDetail"
	PrayerService_GetHajiDoaRingkasList_FullMethodName        = "/islamic.v1.PrayerService/GetHajiDoaRingkasList"
	PrayerService_GetHajiDoaPanjangList_FullMethodName        = "/islamic.v1.PrayerService/GetHajiDoaPanjangList"
	PrayerService_GetHajiDoaPanjangBacaanList_FullMethodName  = "/islamic.v1.PrayerService/GetHajiDoaPanjangBacaanList"
	PrayerService_GetHajiHikmahList_FullMethodName            = "/islamic.v1.PrayerService/GetHajiHikmahList"
	PrayerService_GetHajiNewsList_FullMethodName              = "/islamic.v1.PrayerService/GetHajiNewsList"
	PrayerService_GetHajiLandmarkList_FullMethodName          = "/islamic.v1.PrayerService/GetHajiLandmarkList"
	PrayerService_GetHajiLandmarkDetail_FullMethodName        = "/islamic.v1.PrayerService/GetHajiLandmarkDetail"
	PrayerService_GetUmrahUrutanList_FullMethodName           = "/islamic.v1.PrayerService/GetUmrahUrutanList"
	PrayerService_GetUmrahUrutanDetail_FullMethodName         = "/islamic.v1.PrayerService/GetUmrahUrutanDetail"
	PrayerService_GetUmrahDoaRingkasList_FullMethodName       = "/islamic.v1.PrayerService/GetUmrahDoaRingkasList"
	PrayerService_GetUmrahDoaPanjangList_FullMethodName       = "/islamic.v1.PrayerService/GetUmrahDoaPanjangList"
	PrayerService_GetUmrahDoaPanjangBacaanList_FullMethodName = "/islamic.v1.PrayerService/GetUmrahDoaPanjangBacaanList"
	PrayerService_GetUmrahHikmahList_FullMethodName           = "/islamic.v1.PrayerService/GetUmrahHikmahList"
	PrayerService_GetUmrahLandmarkList_FullMethodName         = "/islamic.v1.PrayerService/GetUmrahLandmarkList"
	PrayerService_GetUmrahLandmarkDetail_FullMethodName       = "/islamic.v1.PrayerService/GetUmrahLandmarkDetail"
)

// PrayerServiceClient is the client API for PrayerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 祷告时间服务定义
type PrayerServiceClient interface {
	// 获取日历数据
	GetCalendar(ctx context.Context, in *CalendarReq, opts ...grpc.CallOption) (*CalendarRes, error)
	// 批量获取多个年月的日历数据
	GetBatchCalendar(ctx context.Context, in *BatchCalendarReq, opts ...grpc.CallOption) (*BatchCalendarRes, error)
	// 获取每天祷告时间（前端自己选择用哪个吧）
	GetDailyPrayerTime(ctx context.Context, in *GetDailyPrayerTimeReq, opts ...grpc.CallOption) (*GetDailyPrayerTimeRes, error)
	// 获取月度祷告时间（前端自己选择用哪个吧）
	GetMonthlyPrayerTimes(ctx context.Context, in *GetMonthlyPrayerTimesReq, opts ...grpc.CallOption) (*GetMonthlyPrayerTimesRes, error)
	// 获取朝觐日程列表
	GetHajiJadwalList(ctx context.Context, in *HajiJadwalListReq, opts ...grpc.CallOption) (*HajiJadwalListRes, error)
	// 获取朝觐日程详情
	GetHajiJadwalDetail(ctx context.Context, in *HajiJadwalDetailReq, opts ...grpc.CallOption) (*HajiJadwalDetailRes, error)
	// 获取朝觐仪式顺序列表
	GetHajiUrutanList(ctx context.Context, in *HajiUrutanListReq, opts ...grpc.CallOption) (*HajiUrutanListRes, error)
	// 获取朝觐仪式顺序详情
	GetHajiUrutanDetail(ctx context.Context, in *HajiUrutanDetailReq, opts ...grpc.CallOption) (*HajiUrutanDetailRes, error)
	// 获取朝觐祈祷文简要列表
	GetHajiDoaRingkasList(ctx context.Context, in *HajiDoaRingkasListReq, opts ...grpc.CallOption) (*HajiDoaRingkasListRes, error)
	// 获取朝觐祈祷文详细列表
	GetHajiDoaPanjangList(ctx context.Context, in *HajiDoaPanjangListReq, opts ...grpc.CallOption) (*HajiDoaPanjangListRes, error)
	// 获取朝觐祈祷文诵读内容列表
	GetHajiDoaPanjangBacaanList(ctx context.Context, in *HajiDoaPanjangBacaanListReq, opts ...grpc.CallOption) (*HajiDoaPanjangBacaanListRes, error)
	// 获取朝觐智慧列表
	GetHajiHikmahList(ctx context.Context, in *HajiHikmahListReq, opts ...grpc.CallOption) (*HajiHikmahListRes, error)
	// 获取朝觐新闻列表
	GetHajiNewsList(ctx context.Context, in *HajiNewsListReq, opts ...grpc.CallOption) (*HajiNewsListRes, error)
	// 获取朝觐地标列表
	GetHajiLandmarkList(ctx context.Context, in *HajiLandmarkListReq, opts ...grpc.CallOption) (*HajiLandmarkListRes, error)
	// 获取朝觐地标详情
	GetHajiLandmarkDetail(ctx context.Context, in *HajiLandmarkDetailReq, opts ...grpc.CallOption) (*HajiLandmarkDetailRes, error)
	// 获取副朝仪式顺序列表
	GetUmrahUrutanList(ctx context.Context, in *UmrahUrutanListReq, opts ...grpc.CallOption) (*UmrahUrutanListRes, error)
	// 获取副朝仪式顺序详情
	GetUmrahUrutanDetail(ctx context.Context, in *UmrahUrutanDetailReq, opts ...grpc.CallOption) (*UmrahUrutanDetailRes, error)
	// 获取副朝祈祷文简要列表
	GetUmrahDoaRingkasList(ctx context.Context, in *UmrahDoaRingkasListReq, opts ...grpc.CallOption) (*UmrahDoaRingkasListRes, error)
	// 获取副朝祈祷文详细列表
	GetUmrahDoaPanjangList(ctx context.Context, in *UmrahDoaPanjangListReq, opts ...grpc.CallOption) (*UmrahDoaPanjangListRes, error)
	// 获取副朝祈祷文诵读内容列表
	GetUmrahDoaPanjangBacaanList(ctx context.Context, in *UmrahDoaPanjangBacaanListReq, opts ...grpc.CallOption) (*UmrahDoaPanjangBacaanListRes, error)
	// 获取副朝智慧列表
	GetUmrahHikmahList(ctx context.Context, in *UmrahHikmahListReq, opts ...grpc.CallOption) (*UmrahHikmahListRes, error)
	// 获取副朝地标列表
	GetUmrahLandmarkList(ctx context.Context, in *UmrahLandmarkListReq, opts ...grpc.CallOption) (*UmrahLandmarkListRes, error)
	// 获取副朝地标详情
	GetUmrahLandmarkDetail(ctx context.Context, in *UmrahLandmarkDetailReq, opts ...grpc.CallOption) (*UmrahLandmarkDetailRes, error)
}

type prayerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPrayerServiceClient(cc grpc.ClientConnInterface) PrayerServiceClient {
	return &prayerServiceClient{cc}
}

func (c *prayerServiceClient) GetCalendar(ctx context.Context, in *CalendarReq, opts ...grpc.CallOption) (*CalendarRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CalendarRes)
	err := c.cc.Invoke(ctx, PrayerService_GetCalendar_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetBatchCalendar(ctx context.Context, in *BatchCalendarReq, opts ...grpc.CallOption) (*BatchCalendarRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchCalendarRes)
	err := c.cc.Invoke(ctx, PrayerService_GetBatchCalendar_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetDailyPrayerTime(ctx context.Context, in *GetDailyPrayerTimeReq, opts ...grpc.CallOption) (*GetDailyPrayerTimeRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDailyPrayerTimeRes)
	err := c.cc.Invoke(ctx, PrayerService_GetDailyPrayerTime_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetMonthlyPrayerTimes(ctx context.Context, in *GetMonthlyPrayerTimesReq, opts ...grpc.CallOption) (*GetMonthlyPrayerTimesRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMonthlyPrayerTimesRes)
	err := c.cc.Invoke(ctx, PrayerService_GetMonthlyPrayerTimes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiJadwalList(ctx context.Context, in *HajiJadwalListReq, opts ...grpc.CallOption) (*HajiJadwalListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HajiJadwalListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetHajiJadwalList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiJadwalDetail(ctx context.Context, in *HajiJadwalDetailReq, opts ...grpc.CallOption) (*HajiJadwalDetailRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HajiJadwalDetailRes)
	err := c.cc.Invoke(ctx, PrayerService_GetHajiJadwalDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiUrutanList(ctx context.Context, in *HajiUrutanListReq, opts ...grpc.CallOption) (*HajiUrutanListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HajiUrutanListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetHajiUrutanList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiUrutanDetail(ctx context.Context, in *HajiUrutanDetailReq, opts ...grpc.CallOption) (*HajiUrutanDetailRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HajiUrutanDetailRes)
	err := c.cc.Invoke(ctx, PrayerService_GetHajiUrutanDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiDoaRingkasList(ctx context.Context, in *HajiDoaRingkasListReq, opts ...grpc.CallOption) (*HajiDoaRingkasListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HajiDoaRingkasListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetHajiDoaRingkasList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiDoaPanjangList(ctx context.Context, in *HajiDoaPanjangListReq, opts ...grpc.CallOption) (*HajiDoaPanjangListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HajiDoaPanjangListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetHajiDoaPanjangList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiDoaPanjangBacaanList(ctx context.Context, in *HajiDoaPanjangBacaanListReq, opts ...grpc.CallOption) (*HajiDoaPanjangBacaanListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HajiDoaPanjangBacaanListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetHajiDoaPanjangBacaanList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiHikmahList(ctx context.Context, in *HajiHikmahListReq, opts ...grpc.CallOption) (*HajiHikmahListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HajiHikmahListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetHajiHikmahList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiNewsList(ctx context.Context, in *HajiNewsListReq, opts ...grpc.CallOption) (*HajiNewsListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HajiNewsListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetHajiNewsList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiLandmarkList(ctx context.Context, in *HajiLandmarkListReq, opts ...grpc.CallOption) (*HajiLandmarkListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HajiLandmarkListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetHajiLandmarkList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetHajiLandmarkDetail(ctx context.Context, in *HajiLandmarkDetailReq, opts ...grpc.CallOption) (*HajiLandmarkDetailRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HajiLandmarkDetailRes)
	err := c.cc.Invoke(ctx, PrayerService_GetHajiLandmarkDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahUrutanList(ctx context.Context, in *UmrahUrutanListReq, opts ...grpc.CallOption) (*UmrahUrutanListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UmrahUrutanListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetUmrahUrutanList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahUrutanDetail(ctx context.Context, in *UmrahUrutanDetailReq, opts ...grpc.CallOption) (*UmrahUrutanDetailRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UmrahUrutanDetailRes)
	err := c.cc.Invoke(ctx, PrayerService_GetUmrahUrutanDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahDoaRingkasList(ctx context.Context, in *UmrahDoaRingkasListReq, opts ...grpc.CallOption) (*UmrahDoaRingkasListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UmrahDoaRingkasListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetUmrahDoaRingkasList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahDoaPanjangList(ctx context.Context, in *UmrahDoaPanjangListReq, opts ...grpc.CallOption) (*UmrahDoaPanjangListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UmrahDoaPanjangListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetUmrahDoaPanjangList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahDoaPanjangBacaanList(ctx context.Context, in *UmrahDoaPanjangBacaanListReq, opts ...grpc.CallOption) (*UmrahDoaPanjangBacaanListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UmrahDoaPanjangBacaanListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetUmrahDoaPanjangBacaanList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahHikmahList(ctx context.Context, in *UmrahHikmahListReq, opts ...grpc.CallOption) (*UmrahHikmahListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UmrahHikmahListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetUmrahHikmahList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahLandmarkList(ctx context.Context, in *UmrahLandmarkListReq, opts ...grpc.CallOption) (*UmrahLandmarkListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UmrahLandmarkListRes)
	err := c.cc.Invoke(ctx, PrayerService_GetUmrahLandmarkList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetUmrahLandmarkDetail(ctx context.Context, in *UmrahLandmarkDetailReq, opts ...grpc.CallOption) (*UmrahLandmarkDetailRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UmrahLandmarkDetailRes)
	err := c.cc.Invoke(ctx, PrayerService_GetUmrahLandmarkDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PrayerServiceServer is the server API for PrayerService service.
// All implementations must embed UnimplementedPrayerServiceServer
// for forward compatibility.
//
// 祷告时间服务定义
type PrayerServiceServer interface {
	// 获取日历数据
	GetCalendar(context.Context, *CalendarReq) (*CalendarRes, error)
	// 批量获取多个年月的日历数据
	GetBatchCalendar(context.Context, *BatchCalendarReq) (*BatchCalendarRes, error)
	// 获取每天祷告时间（前端自己选择用哪个吧）
	GetDailyPrayerTime(context.Context, *GetDailyPrayerTimeReq) (*GetDailyPrayerTimeRes, error)
	// 获取月度祷告时间（前端自己选择用哪个吧）
	GetMonthlyPrayerTimes(context.Context, *GetMonthlyPrayerTimesReq) (*GetMonthlyPrayerTimesRes, error)
	// 获取朝觐日程列表
	GetHajiJadwalList(context.Context, *HajiJadwalListReq) (*HajiJadwalListRes, error)
	// 获取朝觐日程详情
	GetHajiJadwalDetail(context.Context, *HajiJadwalDetailReq) (*HajiJadwalDetailRes, error)
	// 获取朝觐仪式顺序列表
	GetHajiUrutanList(context.Context, *HajiUrutanListReq) (*HajiUrutanListRes, error)
	// 获取朝觐仪式顺序详情
	GetHajiUrutanDetail(context.Context, *HajiUrutanDetailReq) (*HajiUrutanDetailRes, error)
	// 获取朝觐祈祷文简要列表
	GetHajiDoaRingkasList(context.Context, *HajiDoaRingkasListReq) (*HajiDoaRingkasListRes, error)
	// 获取朝觐祈祷文详细列表
	GetHajiDoaPanjangList(context.Context, *HajiDoaPanjangListReq) (*HajiDoaPanjangListRes, error)
	// 获取朝觐祈祷文诵读内容列表
	GetHajiDoaPanjangBacaanList(context.Context, *HajiDoaPanjangBacaanListReq) (*HajiDoaPanjangBacaanListRes, error)
	// 获取朝觐智慧列表
	GetHajiHikmahList(context.Context, *HajiHikmahListReq) (*HajiHikmahListRes, error)
	// 获取朝觐新闻列表
	GetHajiNewsList(context.Context, *HajiNewsListReq) (*HajiNewsListRes, error)
	// 获取朝觐地标列表
	GetHajiLandmarkList(context.Context, *HajiLandmarkListReq) (*HajiLandmarkListRes, error)
	// 获取朝觐地标详情
	GetHajiLandmarkDetail(context.Context, *HajiLandmarkDetailReq) (*HajiLandmarkDetailRes, error)
	// 获取副朝仪式顺序列表
	GetUmrahUrutanList(context.Context, *UmrahUrutanListReq) (*UmrahUrutanListRes, error)
	// 获取副朝仪式顺序详情
	GetUmrahUrutanDetail(context.Context, *UmrahUrutanDetailReq) (*UmrahUrutanDetailRes, error)
	// 获取副朝祈祷文简要列表
	GetUmrahDoaRingkasList(context.Context, *UmrahDoaRingkasListReq) (*UmrahDoaRingkasListRes, error)
	// 获取副朝祈祷文详细列表
	GetUmrahDoaPanjangList(context.Context, *UmrahDoaPanjangListReq) (*UmrahDoaPanjangListRes, error)
	// 获取副朝祈祷文诵读内容列表
	GetUmrahDoaPanjangBacaanList(context.Context, *UmrahDoaPanjangBacaanListReq) (*UmrahDoaPanjangBacaanListRes, error)
	// 获取副朝智慧列表
	GetUmrahHikmahList(context.Context, *UmrahHikmahListReq) (*UmrahHikmahListRes, error)
	// 获取副朝地标列表
	GetUmrahLandmarkList(context.Context, *UmrahLandmarkListReq) (*UmrahLandmarkListRes, error)
	// 获取副朝地标详情
	GetUmrahLandmarkDetail(context.Context, *UmrahLandmarkDetailReq) (*UmrahLandmarkDetailRes, error)
	mustEmbedUnimplementedPrayerServiceServer()
}

// UnimplementedPrayerServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPrayerServiceServer struct{}

func (UnimplementedPrayerServiceServer) GetCalendar(context.Context, *CalendarReq) (*CalendarRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCalendar not implemented")
}
func (UnimplementedPrayerServiceServer) GetBatchCalendar(context.Context, *BatchCalendarReq) (*BatchCalendarRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBatchCalendar not implemented")
}
func (UnimplementedPrayerServiceServer) GetDailyPrayerTime(context.Context, *GetDailyPrayerTimeReq) (*GetDailyPrayerTimeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyPrayerTime not implemented")
}
func (UnimplementedPrayerServiceServer) GetMonthlyPrayerTimes(context.Context, *GetMonthlyPrayerTimesReq) (*GetMonthlyPrayerTimesRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMonthlyPrayerTimes not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiJadwalList(context.Context, *HajiJadwalListReq) (*HajiJadwalListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiJadwalList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiJadwalDetail(context.Context, *HajiJadwalDetailReq) (*HajiJadwalDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiJadwalDetail not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiUrutanList(context.Context, *HajiUrutanListReq) (*HajiUrutanListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiUrutanList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiUrutanDetail(context.Context, *HajiUrutanDetailReq) (*HajiUrutanDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiUrutanDetail not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiDoaRingkasList(context.Context, *HajiDoaRingkasListReq) (*HajiDoaRingkasListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiDoaRingkasList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiDoaPanjangList(context.Context, *HajiDoaPanjangListReq) (*HajiDoaPanjangListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiDoaPanjangList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiDoaPanjangBacaanList(context.Context, *HajiDoaPanjangBacaanListReq) (*HajiDoaPanjangBacaanListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiDoaPanjangBacaanList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiHikmahList(context.Context, *HajiHikmahListReq) (*HajiHikmahListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiHikmahList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiNewsList(context.Context, *HajiNewsListReq) (*HajiNewsListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiNewsList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiLandmarkList(context.Context, *HajiLandmarkListReq) (*HajiLandmarkListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiLandmarkList not implemented")
}
func (UnimplementedPrayerServiceServer) GetHajiLandmarkDetail(context.Context, *HajiLandmarkDetailReq) (*HajiLandmarkDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHajiLandmarkDetail not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahUrutanList(context.Context, *UmrahUrutanListReq) (*UmrahUrutanListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahUrutanList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahUrutanDetail(context.Context, *UmrahUrutanDetailReq) (*UmrahUrutanDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahUrutanDetail not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahDoaRingkasList(context.Context, *UmrahDoaRingkasListReq) (*UmrahDoaRingkasListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahDoaRingkasList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahDoaPanjangList(context.Context, *UmrahDoaPanjangListReq) (*UmrahDoaPanjangListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahDoaPanjangList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahDoaPanjangBacaanList(context.Context, *UmrahDoaPanjangBacaanListReq) (*UmrahDoaPanjangBacaanListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahDoaPanjangBacaanList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahHikmahList(context.Context, *UmrahHikmahListReq) (*UmrahHikmahListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahHikmahList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahLandmarkList(context.Context, *UmrahLandmarkListReq) (*UmrahLandmarkListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahLandmarkList not implemented")
}
func (UnimplementedPrayerServiceServer) GetUmrahLandmarkDetail(context.Context, *UmrahLandmarkDetailReq) (*UmrahLandmarkDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUmrahLandmarkDetail not implemented")
}
func (UnimplementedPrayerServiceServer) mustEmbedUnimplementedPrayerServiceServer() {}
func (UnimplementedPrayerServiceServer) testEmbeddedByValue()                       {}

// UnsafePrayerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PrayerServiceServer will
// result in compilation errors.
type UnsafePrayerServiceServer interface {
	mustEmbedUnimplementedPrayerServiceServer()
}

func RegisterPrayerServiceServer(s grpc.ServiceRegistrar, srv PrayerServiceServer) {
	// If the following call pancis, it indicates UnimplementedPrayerServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PrayerService_ServiceDesc, srv)
}

func _PrayerService_GetCalendar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalendarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetCalendar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetCalendar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetCalendar(ctx, req.(*CalendarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetBatchCalendar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCalendarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetBatchCalendar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetBatchCalendar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetBatchCalendar(ctx, req.(*BatchCalendarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetDailyPrayerTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyPrayerTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetDailyPrayerTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetDailyPrayerTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetDailyPrayerTime(ctx, req.(*GetDailyPrayerTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetMonthlyPrayerTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMonthlyPrayerTimesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetMonthlyPrayerTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetMonthlyPrayerTimes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetMonthlyPrayerTimes(ctx, req.(*GetMonthlyPrayerTimesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiJadwalList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiJadwalListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiJadwalList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetHajiJadwalList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiJadwalList(ctx, req.(*HajiJadwalListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiJadwalDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiJadwalDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiJadwalDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetHajiJadwalDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiJadwalDetail(ctx, req.(*HajiJadwalDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiUrutanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiUrutanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiUrutanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetHajiUrutanList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiUrutanList(ctx, req.(*HajiUrutanListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiUrutanDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiUrutanDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiUrutanDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetHajiUrutanDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiUrutanDetail(ctx, req.(*HajiUrutanDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiDoaRingkasList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiDoaRingkasListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiDoaRingkasList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetHajiDoaRingkasList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiDoaRingkasList(ctx, req.(*HajiDoaRingkasListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiDoaPanjangList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiDoaPanjangListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiDoaPanjangList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetHajiDoaPanjangList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiDoaPanjangList(ctx, req.(*HajiDoaPanjangListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiDoaPanjangBacaanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiDoaPanjangBacaanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiDoaPanjangBacaanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetHajiDoaPanjangBacaanList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiDoaPanjangBacaanList(ctx, req.(*HajiDoaPanjangBacaanListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiHikmahList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiHikmahListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiHikmahList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetHajiHikmahList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiHikmahList(ctx, req.(*HajiHikmahListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiNewsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiNewsListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiNewsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetHajiNewsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiNewsList(ctx, req.(*HajiNewsListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiLandmarkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiLandmarkListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiLandmarkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetHajiLandmarkList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiLandmarkList(ctx, req.(*HajiLandmarkListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetHajiLandmarkDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HajiLandmarkDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetHajiLandmarkDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetHajiLandmarkDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetHajiLandmarkDetail(ctx, req.(*HajiLandmarkDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahUrutanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahUrutanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahUrutanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetUmrahUrutanList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahUrutanList(ctx, req.(*UmrahUrutanListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahUrutanDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahUrutanDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahUrutanDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetUmrahUrutanDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahUrutanDetail(ctx, req.(*UmrahUrutanDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahDoaRingkasList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahDoaRingkasListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahDoaRingkasList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetUmrahDoaRingkasList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahDoaRingkasList(ctx, req.(*UmrahDoaRingkasListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahDoaPanjangList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahDoaPanjangListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahDoaPanjangList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetUmrahDoaPanjangList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahDoaPanjangList(ctx, req.(*UmrahDoaPanjangListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahDoaPanjangBacaanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahDoaPanjangBacaanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahDoaPanjangBacaanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetUmrahDoaPanjangBacaanList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahDoaPanjangBacaanList(ctx, req.(*UmrahDoaPanjangBacaanListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahHikmahList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahHikmahListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahHikmahList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetUmrahHikmahList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahHikmahList(ctx, req.(*UmrahHikmahListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahLandmarkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahLandmarkListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahLandmarkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetUmrahLandmarkList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahLandmarkList(ctx, req.(*UmrahLandmarkListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetUmrahLandmarkDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UmrahLandmarkDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetUmrahLandmarkDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrayerService_GetUmrahLandmarkDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetUmrahLandmarkDetail(ctx, req.(*UmrahLandmarkDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PrayerService_ServiceDesc is the grpc.ServiceDesc for PrayerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PrayerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.PrayerService",
	HandlerType: (*PrayerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCalendar",
			Handler:    _PrayerService_GetCalendar_Handler,
		},
		{
			MethodName: "GetBatchCalendar",
			Handler:    _PrayerService_GetBatchCalendar_Handler,
		},
		{
			MethodName: "GetDailyPrayerTime",
			Handler:    _PrayerService_GetDailyPrayerTime_Handler,
		},
		{
			MethodName: "GetMonthlyPrayerTimes",
			Handler:    _PrayerService_GetMonthlyPrayerTimes_Handler,
		},
		{
			MethodName: "GetHajiJadwalList",
			Handler:    _PrayerService_GetHajiJadwalList_Handler,
		},
		{
			MethodName: "GetHajiJadwalDetail",
			Handler:    _PrayerService_GetHajiJadwalDetail_Handler,
		},
		{
			MethodName: "GetHajiUrutanList",
			Handler:    _PrayerService_GetHajiUrutanList_Handler,
		},
		{
			MethodName: "GetHajiUrutanDetail",
			Handler:    _PrayerService_GetHajiUrutanDetail_Handler,
		},
		{
			MethodName: "GetHajiDoaRingkasList",
			Handler:    _PrayerService_GetHajiDoaRingkasList_Handler,
		},
		{
			MethodName: "GetHajiDoaPanjangList",
			Handler:    _PrayerService_GetHajiDoaPanjangList_Handler,
		},
		{
			MethodName: "GetHajiDoaPanjangBacaanList",
			Handler:    _PrayerService_GetHajiDoaPanjangBacaanList_Handler,
		},
		{
			MethodName: "GetHajiHikmahList",
			Handler:    _PrayerService_GetHajiHikmahList_Handler,
		},
		{
			MethodName: "GetHajiNewsList",
			Handler:    _PrayerService_GetHajiNewsList_Handler,
		},
		{
			MethodName: "GetHajiLandmarkList",
			Handler:    _PrayerService_GetHajiLandmarkList_Handler,
		},
		{
			MethodName: "GetHajiLandmarkDetail",
			Handler:    _PrayerService_GetHajiLandmarkDetail_Handler,
		},
		{
			MethodName: "GetUmrahUrutanList",
			Handler:    _PrayerService_GetUmrahUrutanList_Handler,
		},
		{
			MethodName: "GetUmrahUrutanDetail",
			Handler:    _PrayerService_GetUmrahUrutanDetail_Handler,
		},
		{
			MethodName: "GetUmrahDoaRingkasList",
			Handler:    _PrayerService_GetUmrahDoaRingkasList_Handler,
		},
		{
			MethodName: "GetUmrahDoaPanjangList",
			Handler:    _PrayerService_GetUmrahDoaPanjangList_Handler,
		},
		{
			MethodName: "GetUmrahDoaPanjangBacaanList",
			Handler:    _PrayerService_GetUmrahDoaPanjangBacaanList_Handler,
		},
		{
			MethodName: "GetUmrahHikmahList",
			Handler:    _PrayerService_GetUmrahHikmahList_Handler,
		},
		{
			MethodName: "GetUmrahLandmarkList",
			Handler:    _PrayerService_GetUmrahLandmarkList_Handler,
		},
		{
			MethodName: "GetUmrahLandmarkDetail",
			Handler:    _PrayerService_GetUmrahLandmarkDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/prayer.proto",
}
