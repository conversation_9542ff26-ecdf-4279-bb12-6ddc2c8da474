package islamic

import (
	"context"
	"fmt"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"
	"halalplus/utility/page"
	"halalplus/utility/token"
	"time"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

type ControllerPrayer struct {
	v1.UnimplementedPrayerServiceServer
}

func (*ControllerPrayer) GetCalendar(ctx context.Context, req *v1.CalendarReq) (res *v1.CalendarRes, err error) {
	// 参数验证
	if req.Year <= 0 || req.Month <= 0 || req.Month > 12 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "invalid year or month")
	}

	// 转换protobuf请求为内部参数
	methodCode := req.MethodCode
	if methodCode == "" || methodCode == "AUTO" {
		methodCode = "UMMUL_QURA"
	}

	input := &model.CalendarGetInput{
		Year:           int32(req.Year),
		Month:          int32(req.Month),
		MethodCode:     methodCode,
		DateAdjustment: req.DateAdjustment,
	}

	// 调用service层获取日历数据
	calendarData, err := service.Prayer().GetCalendar(ctx, input)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 使用gconv.Structs转换数据结构
	calendarList := make([]*v1.CalendarDateInfo, 0, len(calendarData))
	err = gconv.Structs(calendarData, &calendarList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.CalendarRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.CalendarData{
			List: calendarList,
		},
	}, nil
}

// GetBatchCalendar 批量获取多个年月的日历数据
func (*ControllerPrayer) GetBatchCalendar(ctx context.Context, req *v1.BatchCalendarReq) (res *v1.BatchCalendarRes, err error) {
	// 参数验证
	if len(req.YearMonths) == 0 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "year_months cannot be empty")
	}

	// 限制批量查询的数量，避免过多请求
	if len(req.YearMonths) > 12 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "too many year_months, maximum 12 allowed")
	}

	// 转换protobuf请求为内部参数
	methodCode := req.MethodCode
	if methodCode == "" || methodCode == "AUTO" {
		methodCode = "UMMUL_QURA"
	}

	input := &model.BatchCalendarGetInput{
		YearMonths:     req.YearMonths,
		MethodCode:     methodCode,
		DateAdjustment: req.DateAdjustment,
	}

	// 调用service层获取批量日历数据
	batchCalendarData, err := service.Prayer().GetBatchCalendar(ctx, input)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换
	calendarsMap := make(map[string]*v1.CalendarData)
	for yearMonth, calendarDateInfoList := range batchCalendarData {
		// 使用gconv.Structs转换数据结构
		calendarList := make([]*v1.CalendarDateInfo, 0, len(calendarDateInfoList))
		err = gconv.Structs(calendarDateInfoList, &calendarList)
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("failed to convert calendar data for %s: %v", yearMonth, err))
			continue
		}

		calendarsMap[yearMonth] = &v1.CalendarData{
			List: calendarList,
		}
	}

	return &v1.BatchCalendarRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.BatchCalendarData{
			Calendars: calendarsMap,
		},
	}, nil
}

// GetDailyPrayerTimes 获取每日祷告时间
func (*ControllerPrayer) GetDailyPrayerTime(ctx context.Context, req *v1.GetDailyPrayerTimeReq) (res *v1.GetDailyPrayerTimeRes, err error) {
	dateTime, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		g.Log().Error(ctx, "invalid date format:", err)
		return nil, err
	}

	input := &model.DailyPrayerTimeInput{
		Year:           dateTime.Year(),
		Month:          int(dateTime.Month()),
		Day:            dateTime.Day(),
		Latitude:       req.Latitude,
		Longitude:      req.Longitude,
		Timezone:       req.Timezone,
		MethodCode:     req.MethodCode,
		DateAdjustment: int(req.DateAdjustment),
	}

	output, err := service.Prayer().GetDailyPrayerTime(ctx, input)
	if err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	var prayerTimesData *v1.PrayerTimeData
	if err = gconv.Struct(output, &prayerTimesData); err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	return &v1.GetDailyPrayerTimeRes{
		Code: 200,
		Msg:  "success",
		Data: prayerTimesData,
	}, nil
}

// GetMonthlyPrayerTimes 获取月度祷告时间
func (*ControllerPrayer) GetMonthlyPrayerTimes(ctx context.Context, req *v1.GetMonthlyPrayerTimesReq) (res *v1.GetMonthlyPrayerTimesRes, err error) {

	input := &model.MonthlyPrayerTimesInput{
		Year:           int(req.Year),
		Month:          int(req.Month),
		Latitude:       req.Latitude,
		Longitude:      req.Longitude,
		Timezone:       req.Timezone,
		MethodCode:     req.MethodCode,
		DateAdjustment: int(req.DateAdjustment),
	}

	output, err := service.Prayer().GetMonthlyPrayerTimes(ctx, input)
	if err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	var timeList []*v1.PrayerTimeData
	if err = gconv.Structs(output, &timeList); err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	return &v1.GetMonthlyPrayerTimesRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.MonthlyPrayerTimesData{
			List: timeList,
		},
	}, nil
}

// GetHajiJadwalList 获取朝觐日程列表
func (*ControllerPrayer) GetHajiJadwalList(ctx context.Context, req *v1.HajiJadwalListReq) (res *v1.HajiJadwalListRes, err error) {
	languageId := token.GetLanguageId(ctx)

	output, err := service.Prayer().GetHajiJadwalList(ctx, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var jadwalList []*v1.HajiJadwalInfo
	err = gconv.Structs(output.List, &jadwalList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiJadwalListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiJadwalListData{
			List:        jadwalList,
			Description: output.Description,
			Year:        output.Year,
		},
	}, nil
}

// GetHajiJadwalDetail 获取朝觐日程详情
func (*ControllerPrayer) GetHajiJadwalDetail(ctx context.Context, req *v1.HajiJadwalDetailReq) (res *v1.HajiJadwalDetailRes, err error) {
	if req.JadwalId == 0 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "jadwal_id cannot be empty")
	}

	languageId := token.GetLanguageId(ctx)

	output, err := service.Prayer().GetHajiJadwalDetail(ctx, req.JadwalId, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var jadwal v1.HajiJadwalInfo
	err = gconv.Struct(output.Jadwal, &jadwal)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiJadwalDetailRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiJadwalDetailData{
			Jadwal: &jadwal,
		},
	}, nil
}

// GetHajiUrutanList 获取朝觐仪式顺序列表
func (*ControllerPrayer) GetHajiUrutanList(ctx context.Context, req *v1.HajiUrutanListReq) (res *v1.HajiUrutanListRes, err error) {
	languageId := token.GetLanguageId(ctx)

	output, err := service.Prayer().GetHajiUrutanList(ctx, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var urutanList []*v1.HajiUrutanInfo
	err = gconv.Structs(output.List, &urutanList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiUrutanListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiUrutanListData{
			List: urutanList,
		},
	}, nil
}

// GetHajiUrutanDetail 获取朝觐仪式顺序详情
func (*ControllerPrayer) GetHajiUrutanDetail(ctx context.Context, req *v1.HajiUrutanDetailReq) (res *v1.HajiUrutanDetailRes, err error) {
	if req.UrutanId == 0 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "urutan_id cannot be empty")
	}

	languageId := token.GetLanguageId(ctx)

	output, err := service.Prayer().GetHajiUrutanDetail(ctx, req.UrutanId, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var urutan v1.HajiUrutanInfo
	err = gconv.Struct(output.Urutan, &urutan)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiUrutanDetailRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiUrutanDetailData{
			Urutan: &urutan,
		},
	}, nil
}

// GetUmrahUrutanList 获取副朝仪式顺序列表
func (*ControllerPrayer) GetUmrahUrutanList(ctx context.Context, req *v1.UmrahUrutanListReq) (res *v1.UmrahUrutanListRes, err error) {
	languageId := token.GetLanguageId(ctx)

	output, err := service.Prayer().GetUmrahUrutanList(ctx, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var urutanList []*v1.UmrahUrutanInfo
	err = gconv.Structs(output.List, &urutanList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.UmrahUrutanListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UmrahUrutanListData{
			List: urutanList,
		},
	}, nil
}

// GetUmrahUrutanDetail 获取副朝仪式顺序详情
func (*ControllerPrayer) GetUmrahUrutanDetail(ctx context.Context, req *v1.UmrahUrutanDetailReq) (res *v1.UmrahUrutanDetailRes, err error) {
	if req.UrutanId == 0 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "urutan_id cannot be empty")
	}

	languageId := token.GetLanguageId(ctx)

	output, err := service.Prayer().GetUmrahUrutanDetail(ctx, req.UrutanId, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var urutan v1.UmrahUrutanInfo
	err = gconv.Struct(output.Urutan, &urutan)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.UmrahUrutanDetailRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UmrahUrutanDetailData{
			Urutan: &urutan,
		},
	}, nil
}

// GetHajiDoaRingkasList 获取朝觐祈祷文简要列表
func (*ControllerPrayer) GetHajiDoaRingkasList(ctx context.Context, req *v1.HajiDoaRingkasListReq) (res *v1.HajiDoaRingkasListRes, err error) {
	output, err := service.Prayer().GetHajiDoaRingkasList(ctx)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var doaList []*v1.HajiDoaRingkasInfo
	err = gconv.Structs(output, &doaList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiDoaRingkasListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiDoaRingkasListData{
			List: doaList,
		},
	}, nil
}

// GetHajiDoaPanjangList 获取朝觐祈祷文详细列表
func (*ControllerPrayer) GetHajiDoaPanjangList(ctx context.Context, req *v1.HajiDoaPanjangListReq) (res *v1.HajiDoaPanjangListRes, err error) {
	output, err := service.Prayer().GetHajiDoaPanjangList(ctx)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var doaList []*v1.HajiDoaPanjangInfo
	err = gconv.Structs(output, &doaList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiDoaPanjangListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiDoaPanjangListData{
			List: doaList,
		},
	}, nil
}

// GetHajiDoaPanjangBacaanList 获取朝觐祈祷文诵读内容列表
func (*ControllerPrayer) GetHajiDoaPanjangBacaanList(ctx context.Context, req *v1.HajiDoaPanjangBacaanListReq) (res *v1.HajiDoaPanjangBacaanListRes, err error) {
	if req.DoaId == 0 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "doa_id cannot be empty")
	}

	output, err := service.Prayer().GetHajiDoaPanjangBacaanList(ctx, req.DoaId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var bacaanList []*v1.HajiDoaPanjangBacaanInfo
	err = gconv.Structs(output.List, &bacaanList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiDoaPanjangBacaanListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiDoaPanjangBacaanListData{
			List:    bacaanList,
			DoaNo:   int32(output.DoaNo),
			DoaName: output.DoaName,
		},
	}, nil
}

// GetUmrahDoaRingkasList 获取副朝祈祷文简要列表
func (*ControllerPrayer) GetUmrahDoaRingkasList(ctx context.Context, req *v1.UmrahDoaRingkasListReq) (res *v1.UmrahDoaRingkasListRes, err error) {
	output, err := service.Prayer().GetUmrahDoaRingkasList(ctx)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var doaList []*v1.UmrahDoaRingkasInfo
	err = gconv.Structs(output, &doaList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.UmrahDoaRingkasListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UmrahDoaRingkasListData{
			List: doaList,
		},
	}, nil
}

// GetUmrahDoaPanjangList 获取副朝祈祷文详细列表
func (*ControllerPrayer) GetUmrahDoaPanjangList(ctx context.Context, req *v1.UmrahDoaPanjangListReq) (res *v1.UmrahDoaPanjangListRes, err error) {
	output, err := service.Prayer().GetUmrahDoaPanjangList(ctx)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var doaList []*v1.UmrahDoaPanjangInfo
	err = gconv.Structs(output, &doaList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.UmrahDoaPanjangListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UmrahDoaPanjangListData{
			List: doaList,
		},
	}, nil
}

// GetUmrahDoaPanjangBacaanList 获取副朝祈祷文诵读内容列表
func (*ControllerPrayer) GetUmrahDoaPanjangBacaanList(ctx context.Context, req *v1.UmrahDoaPanjangBacaanListReq) (res *v1.UmrahDoaPanjangBacaanListRes, err error) {
	if req.DoaId == 0 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "doa_id cannot be empty")
	}

	output, err := service.Prayer().GetUmrahDoaPanjangBacaanList(ctx, req.DoaId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var bacaanList []*v1.UmrahDoaPanjangBacaanInfo
	err = gconv.Structs(output.List, &bacaanList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.UmrahDoaPanjangBacaanListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UmrahDoaPanjangBacaanListData{
			List:    bacaanList,
			DoaNo:   int32(output.DoaNo),
			DoaName: output.DoaName,
		},
	}, nil
}

// GetHajiHikmahList 获取朝觐智慧列表
func (*ControllerPrayer) GetHajiHikmahList(ctx context.Context, req *v1.HajiHikmahListReq) (res *v1.HajiHikmahListRes, err error) {
	languageId := token.GetLanguageId(ctx)

	output, err := service.Prayer().GetHajiHikmahList(ctx, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var hikmahList []*v1.HajiHikmahInfo
	err = gconv.Structs(output.List, &hikmahList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiHikmahListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiHikmahListData{
			List: hikmahList,
		},
	}, nil
}

// GetHajiNewsList 获取朝觐新闻列表
func (*ControllerPrayer) GetHajiNewsList(ctx context.Context, req *v1.HajiNewsListReq) (res *v1.HajiNewsListRes, err error) {
	languageId := token.GetLanguageId(ctx)
	pageParams := page.NormalizePageRequest(req.Page)

	output, err := service.Prayer().GetHajiNewsList(ctx, languageId, pageParams.Page, pageParams.Size)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var newsList []*v1.HajiNewsItem
	err = gconv.Structs(output.List, &newsList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiNewsListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiNewsListData{
			List:      newsList,
			Page:      pageParams.ToPageResponse(output.Total),
			HeaderTag: output.HeaderTag,
		},
	}, nil
}

// GetUmrahHikmahList 获取副朝智慧列表
func (*ControllerPrayer) GetUmrahHikmahList(ctx context.Context, req *v1.UmrahHikmahListReq) (res *v1.UmrahHikmahListRes, err error) {
	languageId := token.GetLanguageId(ctx)

	output, err := service.Prayer().GetUmrahHikmahList(ctx, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var hikmahList []*v1.UmrahHikmahInfo
	err = gconv.Structs(output.List, &hikmahList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.UmrahHikmahListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UmrahHikmahListData{
			List: hikmahList,
		},
	}, nil
}

// GetHajiLandmarkList 获取朝觐地标列表
func (*ControllerPrayer) GetHajiLandmarkList(ctx context.Context, req *v1.HajiLandmarkListReq) (res *v1.HajiLandmarkListRes, err error) {
	languageId := token.GetLanguageId(ctx)
	pageParams := page.NormalizePageRequest(req.Page)

	output, err := service.Prayer().GetHajiLandmarkList(ctx, req.InnerType, languageId, pageParams.Page, pageParams.Size)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var landmarkList []*v1.HajiLandmarkItem
	err = gconv.Structs(output.List, &landmarkList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiLandmarkListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiLandmarkListData{
			List: landmarkList,
			Page: pageParams.ToPageResponse(output.Total),
		},
	}, nil
}

// GetHajiLandmarkDetail 获取朝觐地标详情
func (*ControllerPrayer) GetHajiLandmarkDetail(ctx context.Context, req *v1.HajiLandmarkDetailReq) (res *v1.HajiLandmarkDetailRes, err error) {
	if req.LandmarkId == 0 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "landmark_id cannot be empty")
	}

	languageId := token.GetLanguageId(ctx)

	output, err := service.Prayer().GetHajiLandmarkDetail(ctx, req.LandmarkId, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var landmark *v1.HajiLandmarkInfo
	err = gconv.Struct(output, &landmark)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiLandmarkDetailRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiLandmarkDetailData{
			Landmark: landmark,
		},
	}, nil
}

// GetUmrahLandmarkList 获取副朝地标列表
func (*ControllerPrayer) GetUmrahLandmarkList(ctx context.Context, req *v1.UmrahLandmarkListReq) (res *v1.UmrahLandmarkListRes, err error) {
	languageId := token.GetLanguageId(ctx)
	pageParams := page.NormalizePageRequest(req.Page)

	output, err := service.Prayer().GetUmrahLandmarkList(ctx, req.InnerType, languageId, pageParams.Page, pageParams.Size)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var landmarkList []*v1.UmrahLandmarkItem
	err = gconv.Structs(output.List, &landmarkList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.UmrahLandmarkListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UmrahLandmarkListData{
			List: landmarkList,
			Page: pageParams.ToPageResponse(output.Total),
		},
	}, nil
}

// GetUmrahLandmarkDetail 获取副朝地标详情
func (*ControllerPrayer) GetUmrahLandmarkDetail(ctx context.Context, req *v1.UmrahLandmarkDetailReq) (res *v1.UmrahLandmarkDetailRes, err error) {
	if req.LandmarkId == 0 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "landmark_id cannot be empty")
	}

	languageId := token.GetLanguageId(ctx)

	output, err := service.Prayer().GetUmrahLandmarkDetail(ctx, req.LandmarkId, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var landmark *v1.UmrahLandmarkInfo
	err = gconv.Struct(output, &landmark)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.UmrahLandmarkDetailRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UmrahLandmarkDetailData{
			Landmark: landmark,
		},
	}, nil
}
