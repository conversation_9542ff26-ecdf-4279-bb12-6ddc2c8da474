// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// hajiNewsTagArticleDao is the data access object for the table haji_news_tag_article.
// You can define custom methods on it to extend its functionality as needed.
type hajiNewsTagArticleDao struct {
	*internal.HajiNewsTagArticleDao
}

var (
	// HajiNewsTagArticle is a globally accessible object for table haji_news_tag_article operations.
	HajiNewsTagArticle = hajiNewsTagArticleDao{internal.NewHajiNewsTagArticleDao()}
)

// Add your custom methods and functionality below.
