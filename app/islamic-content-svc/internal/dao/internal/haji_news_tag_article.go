// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HajiNewsTagArticleDao is the data access object for the table haji_news_tag_article.
type HajiNewsTagArticleDao struct {
	table    string                    // table is the underlying table name of the DAO.
	group    string                    // group is the database configuration group name of the current DAO.
	columns  HajiNewsTagArticleColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler        // handlers for customized model modification.
}

// HajiNewsTagArticleColumns defines and stores column names for the table haji_news_tag_article.
type HajiNewsTagArticleColumns struct {
	Id         string // 主键ID
	TagId      string // 标签ID，关联haji_news_tag.id
	ArticleId  string // 文章ID，关联news_article.id
	CreateTime string // 创建时间（毫秒时间戳）
	UpdateTime string // 更新时间（毫秒时间戳）
}

// hajiNewsTagArticleColumns holds the columns for the table haji_news_tag_article.
var hajiNewsTagArticleColumns = HajiNewsTagArticleColumns{
	Id:         "id",
	TagId:      "tag_id",
	ArticleId:  "article_id",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewHajiNewsTagArticleDao creates and returns a new DAO object for table data access.
func NewHajiNewsTagArticleDao(handlers ...gdb.ModelHandler) *HajiNewsTagArticleDao {
	return &HajiNewsTagArticleDao{
		group:    "default",
		table:    "haji_news_tag_article",
		columns:  hajiNewsTagArticleColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HajiNewsTagArticleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HajiNewsTagArticleDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HajiNewsTagArticleDao) Columns() HajiNewsTagArticleColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HajiNewsTagArticleDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HajiNewsTagArticleDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HajiNewsTagArticleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
