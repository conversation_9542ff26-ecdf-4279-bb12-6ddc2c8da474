// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// hajiNewsTagDao is the data access object for the table haji_news_tag.
// You can define custom methods on it to extend its functionality as needed.
type hajiNewsTagDao struct {
	*internal.HajiNewsTagDao
}

var (
	// HajiNewsTag is a globally accessible object for table haji_news_tag operations.
	HajiNewsTag = hajiNewsTagDao{internal.NewHajiNewsTagDao()}
)

// Add your custom methods and functionality below.
