// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/model"
)

type (
	IPrayer interface {
		// GetCalendar 获取日历数据
		GetCalendar(ctx context.Context, input *model.CalendarGetInput) ([]*model.CalendarDateInfo, error)
		// GetBatchCalendar 批量获取多个年月的日历数据
		GetBatchCalendar(ctx context.Context, input *model.BatchCalendarGetInput) (map[string][]*model.CalendarDateInfo, error)
		// GetDailyPrayerTime 获取每日祷告时间
		GetDailyPrayerTime(ctx context.Context, in *model.DailyPrayerTimeInput) (*model.PrayerTimeOutput, error)
		// GetMonthlyPrayerTimes 获取月度祷告时间
		GetMonthlyPrayerTimes(ctx context.Context, in *model.MonthlyPrayerTimesInput) ([]*model.PrayerTimeOutput, error)
		// GetHajiJadwalList 获取朝觐日程列表
		GetHajiJadwalList(ctx context.Context, languageId uint) (*model.HajiJadwalListOutput, error)
		// GetHajiJadwalDetail 获取朝觐日程详情
		GetHajiJadwalDetail(ctx context.Context, jadwalId uint64, languageId uint) (*model.HajiJadwalDetailOutput, error)
		// GetHajiUrutanList 获取朝觐仪式顺序列表
		GetHajiUrutanList(ctx context.Context, languageId uint) (*model.HajiUrutanListOutput, error)
		// GetHajiUrutanDetail 获取朝觐仪式顺序详情
		GetHajiUrutanDetail(ctx context.Context, urutanId uint64, languageId uint) (*model.HajiUrutanDetailOutput, error)
		// GetHajiDoaRingkasList 获取朝觐祈祷文简要列表
		GetHajiDoaRingkasList(ctx context.Context) ([]*model.HajiDoaRingkasInfo, error)
		// GetHajiDoaPanjangList 获取朝觐祈祷文详细列表
		GetHajiDoaPanjangList(ctx context.Context) ([]*model.HajiDoaPanjangInfo, error)
		// GetHajiDoaPanjangBacaanList 获取朝觐祈祷文诵读内容列表
		GetHajiDoaPanjangBacaanList(ctx context.Context, doaId uint64) (*model.HajiDoaPanjangBacaanListOutput, error)
		// GetHajiHikmahList 获取朝觐智慧列表
		GetHajiHikmahList(ctx context.Context, languageId uint) (*model.HajiHikmahListOutput, error)
		// GetHajiLandmarkList 获取朝觐地标列表（支持分页）
		GetHajiLandmarkList(ctx context.Context, innerType string, languageId uint, page int, size int) (*model.HajiLandmarkListOutput, error)
		// GetHajiLandmarkDetail 获取朝觐地标详情
		GetHajiLandmarkDetail(ctx context.Context, landmarkId uint64, languageId uint) (*model.HajiLandmarkInfo, error)
		// GetUmrahUrutanList 获取副朝仪式顺序列表
		GetUmrahUrutanList(ctx context.Context, languageId uint) (*model.UmrahUrutanListOutput, error)
		// GetUmrahUrutanDetail 获取副朝仪式顺序详情
		GetUmrahUrutanDetail(ctx context.Context, urutanId uint64, languageId uint) (*model.UmrahUrutanDetailOutput, error)
		// GetUmrahDoaRingkasList 获取副朝祈祷文简要列表
		GetUmrahDoaRingkasList(ctx context.Context) ([]*model.UmrahDoaRingkasInfo, error)
		// GetUmrahDoaPanjangList 获取副朝祈祷文详细列表
		GetUmrahDoaPanjangList(ctx context.Context) ([]*model.UmrahDoaPanjangInfo, error)
		// GetUmrahDoaPanjangBacaanList 获取副朝祈祷文诵读内容列表
		GetUmrahDoaPanjangBacaanList(ctx context.Context, doaId uint64) (*model.UmrahDoaPanjangBacaanListOutput, error)
		// GetUmrahHikmahList 获取副朝智慧列表
		GetUmrahHikmahList(ctx context.Context, languageId uint) (*model.UmrahHikmahListOutput, error)
		// GetUmrahLandmarkList 获取副朝地标列表（支持分页）
		GetUmrahLandmarkList(ctx context.Context, innerType string, languageId uint, page int, size int) (*model.UmrahLandmarkListOutput, error)
		// GetUmrahLandmarkDetail 获取副朝地标详情
		GetUmrahLandmarkDetail(ctx context.Context, landmarkId uint64, languageId uint) (*model.UmrahLandmarkInfo, error)
	}
)

var (
	localPrayer IPrayer
)

func Prayer() IPrayer {
	if localPrayer == nil {
		panic("implement not found for interface IPrayer, forgot register?")
	}
	return localPrayer
}

func RegisterPrayer(i IPrayer) {
	localPrayer = i
}
